{
    'data': 
        {'table_data': 
            {'name': ['a', 'b', 'c'], 'price': [100, 200, 50], 'score': [1.0, 2.0, 3.0]}, 
        'table_id': '2_1_d1decaeb-43e2-4e63-b071-35c54d82ded9', 
        'table_info': {
            'output_df_dict': 
                {'name': {'column_index': 0, 'dirty_count': 0, 'dirty_rate': 0.0, 'dtype': 'string', 'missing_count': 0, 'missing_rate': 0.0}, 'price': {'column_index': 1, 'dirty_count': 0, 'dirty_rate': 0.0, 'dtype': 'int', 'missing_count': 0, 'missing_rate': 0.0}, 'score': {'column_index': 2, 'dirty_count': 1, 'dirty_rate': 0.3333333333333333, 'dtype': 'int', 'missing_count': 1, 'missing_rate': 0.3333333333333333}}, 
            'output_info_dict': 
                {'duplicate_rows': 0, 'overall_dirty_rate': 0.1111111111111111, 'overall_missing_rate': 0.1111111111111111, 'quality_score': 88, 'total_columns': 3, 'total_rows': 3}, 
            'output_type_dict': 
                {'float_count': 0, 'int_count': 2, 'string_count': 1}
        }
    }, 
    'error_code': 0, 'message': '', 'request_id': '347e8c02-c68b-4cd8-97af-bfdc07b8e138'
} 