# Get current hour and minute
current_hour=$(date +%H)
current_minute=$(date +%M)

# # Convert to minutes since midnight
# current_total_minutes=$((10#$current_hour * 60 + 10#$current_minute))
# start_total_minutes=$((8 * 60 + 30))   # 8:30am = 510
# end_total_minutes=$((17 * 60 + 30))    # 5:30pm = 1050

# if [ "$current_total_minutes" -gt "$start_total_minutes" ] && [ "$current_total_minutes" -le "$end_total_minutes" ]; then
#   echo "⛔️ Commits are only allowed before 08:30 or after 17:30."
#   exit 1
# fi

npx lint-staged
