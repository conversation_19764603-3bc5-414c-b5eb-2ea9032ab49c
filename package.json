{"name": "data_analysis", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "prepare": "husky"}, "lint-staged": {"**/*": ["eslint src --fix", "prettier --write --ignore-unknown"]}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.6.0", "lodash": "^4.17.21", "papaparse": "^5.5.3", "pinia": "^3.0.2", "plotly.js-dist": "^3.0.1", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "vue-sheet": "^1.0.22", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.1.0", "globals": "^16.2.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}