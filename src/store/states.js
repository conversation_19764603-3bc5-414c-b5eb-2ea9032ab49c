import { computed, ref } from 'vue'
import { defineStore } from 'pinia'
import { v4 as uuidv4 } from 'uuid'

const getDefaultStates = () => {
  return [
    {
      componentId: uuidv4(),
      componentName: '目标指标',
      indices: [],
    },
    {
      componentId: uuidv4(),
      componentName: '目标维度',
      dimensions: [],
    },
    {
      componentId: uuidv4(),
      componentName: '粒度',
      granularity: '',
      granularityId: '',
      granularityName: '',
    },
    {
      componentId: uuidv4(),
      componentName: '日期',
      dates: [],
    },
    {
      componentId: uuidv4(),
      componentName: 'trendAnalysis',
      selectedTrends: [],
      results: null,
    },
    {
      componentId: uuidv4(),
      componentName: 'corrAnalysis',
      selectedObjects: [],
      results: null,
    },
    {
      componentId: uuidv4(),
      componentName: 'table',
      activeSheet: '',
      sheetData: [],
      semanticsConfig: {},
    },
    {
      componentId: uuidv4(),
      componentName: 'dashboardCharts',
      charts: [],
    },
  ]
}

export const useStates = defineStore('states', () => {
  const pageName = ref('')
  const title = ref('')
  const taskName = ref('tmp') // Default task name for guided analysis
  const taskId = ref('1') // Task ID from create_task API response
  const defaultStates = getDefaultStates()
  const stateIds = ref(new Set(defaultStates.map((state) => state.componentId)))
  const states = ref(defaultStates)
  const chatMessages = ref([])
  const username = ref('')
  const userRole = ref('') // 'admin' or ''
  // const authToken = ref('a1234567890123456789012345678901234567890')
  const tokenExpireTime = ref('')
  const userInfo = ref(null)
  const availableIndicators = ref([])
  const availableDimensions = ref([])

  // Category management for hierarchical selection
  const availableCategories = ref([])
  const selectedCategories = ref({
    indicators: {
      category_lv_1: '',
      category_lv_2: '',
      category_lv_3: '',
    },
    dimensions: {
      category_lv_1: '',
      category_lv_2: '',
      category_lv_3: '',
    },
  })

  // System error state for global error mask
  const systemError = ref({
    show: false,
    title: '',
    message: '',
    errorCode: null,
    retryAction: null,
    timestamp: null,
  })

  // App information state for app info panel
  const appInfo = ref({
    version: '1.0.0',
    releaseDate: '2025-01-15',
    updates: [
      {
        id: 1,
        version: '1.0.0',
        date: '2025-01-15',
        title: '初始版本发布',
        description: '智能统一归因平台正式上线，支持引导式数据分析和可视化功能',
        features: ['引导式数据配置界面', '多维度数据筛选', '实时数据可视化', '智能图表生成'],
      },
      {
        id: 2,
        version: '0.9.5',
        date: '2025-01-10',
        title: '测试版本优化',
        description: '修复已知问题，优化用户体验',
        features: [
          '修复数据加载异常问题',
          '优化浮动窗口交互',
          '增强错误处理机制',
          '改进响应式布局',
        ],
      },
      {
        id: 3,
        version: '0.9.0',
        date: '2025-01-05',
        title: 'Beta版本发布',
        description: '核心功能基本完成，开始内测',
        features: ['完成数据上传功能', '实现基础图表功能', '添加用户认证系统', '建立基础UI框架'],
      },
    ],
    hasNewUpdates: false,
    lastChecked: new Date().toISOString(),
  })

  const getStates = computed(() => {
    return {
      pageName: pageName.value,
      title: title.value,
      timestamp: new Date().getTime(),
      states: states.value,
    }
  })

  function updateComponent(componentId, newStates) {
    this.$patch((s) => {
      if (!s.stateIds.has(componentId)) {
        s.stateIds.add(componentId)
        s.states.push({ componentId, ...newStates })
      } else {
        s.states.forEach((state) => {
          if (state.componentId === componentId) {
            Object.assign(state, newStates)
          }
        })
      }
    })
  }

  function deleteComponent(componentId) {
    this.$patch((s) => {
      s.stateIds.delete(componentId)
      s.states = s.states.filter((state) => state.componentId !== componentId)
    })
  }

  function setChatMessages(msgs) {
    chatMessages.value = msgs
  }

  function appendChatMessage(msg) {
    chatMessages.value.push(msg)
  }

  function clearChatMessages() {
    chatMessages.value = []
  }

  function setUsername(name) {
    username.value = name
  }

  function clearUsername() {
    username.value = ''
  }

  function setUserRole(role) {
    userRole.value = role
  }

  function clearUserRole() {
    userRole.value = ''
  }

  function setAuthToken(token) {
    // authToken.value = token
    localStorage.setItem('token', token)
  }

  function clearAuthToken() {
    // authToken.value = ''
    localStorage.removeItem('token')
  }

  function setTokenExpireTime(expireTime) {
    tokenExpireTime.value = expireTime
  }

  function clearTokenExpireTime() {
    tokenExpireTime.value = ''
  }

  function setUserInfo(info) {
    userInfo.value = info
  }

  function clearUserInfo() {
    userInfo.value = null
  }

  function clearAllAuthData() {
    clearUsername()
    clearUserRole()
    clearAuthToken()
    clearTokenExpireTime()
    clearUserInfo()
    // Clear localStorage as well
    localStorage.removeItem('auth_token')
    localStorage.removeItem('token_expire_time')
    localStorage.removeItem('user_info')
  }

  function setAvailableIndicators(indicators) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableIndicators = indicators
    })
  }

  function clearAvailableIndicators() {
    availableIndicators.value = []
  }

  function setAvailableDimensions(dimensions) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableDimensions = dimensions
    })
  }

  function clearAvailableDimensions() {
    availableDimensions.value = []
  }

  // Category management functions
  function setAvailableCategories(categories) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.availableCategories = categories
    })
  }

  function clearAvailableCategories() {
    availableCategories.value = []
  }

  function setSelectedCategories(type, categoryData) {
    // type: 'indicators' or 'dimensions'
    // categoryData: { category_lv_1, category_lv_2, category_lv_3 }
    this.$patch((s) => {
      s.selectedCategories[type] = { ...s.selectedCategories[type], ...categoryData }
    })
  }

  function clearSelectedCategories(type) {
    // type: 'indicators' or 'dimensions', or undefined to clear all
    this.$patch((s) => {
      if (type) {
        s.selectedCategories[type] = {
          category_lv_1: '',
          category_lv_2: '',
          category_lv_3: '',
        }
      } else {
        s.selectedCategories = {
          indicators: {
            category_lv_1: '',
            category_lv_2: '',
            category_lv_3: '',
          },
          dimensions: {
            category_lv_1: '',
            category_lv_2: '',
            category_lv_3: '',
          },
        }
      }
    })
  }

  // Task name management functions
  function setTaskName(name) {
    taskName.value = name || 'tmp'
  }

  function clearTaskName() {
    taskName.value = 'tmp'
  }

  // Task ID management functions
  function setTaskId(id) {
    taskId.value = id || ''
  }

  function clearTaskId() {
    taskId.value = ''
  }

  // System error management functions
  function showSystemError(title, message, errorCode = null, retryAction = null) {
    systemError.value = {
      show: true,
      title,
      message,
      errorCode,
      retryAction,
      timestamp: new Date().getTime(),
    }
  }

  function hideSystemError() {
    systemError.value = {
      show: false,
      title: '',
      message: '',
      errorCode: null,
      retryAction: null,
      timestamp: null,
    }
  }

  function clearSystemError() {
    hideSystemError()
  }

  // Table data management functions
  function clearSheetData(sheetKey) {
    // Clear sheetData for a specific sheet while preserving other metadata
    const tableState = states.value.find((s) => s.componentName === 'table')
    if (!tableState || !tableState.sheetData || !tableState.sheetData[sheetKey]) {
      return
    }

    this.$patch((s) => {
      const tableComp = s.states.find((state) => state.componentName === 'table')
      if (tableComp && tableComp.sheetData && tableComp.sheetData[sheetKey]) {
        // Clear only the data-related properties, preserve metadata
        const sheetMeta = tableComp.sheetData[sheetKey]
        tableComp.sheetData[sheetKey] = {
          ...sheetMeta,
          columns: [],
          rows: [],
          // Preserve table_id, name, and other metadata
          table_id: sheetMeta.table_id,
          name: sheetMeta.name,
          semanticsConfig: sheetMeta.semanticsConfig || {},
          // Mark as cleared for reload
          isCleared: true,
          clearTimestamp: new Date().toISOString(),
        }
      }
    })
  }

  function updateSheetData(sheetKey, newData) {
    // Update sheet data with fresh data from reload
    const tableState = states.value.find((s) => s.componentName === 'table')
    if (!tableState) {
      return
    }

    this.$patch((s) => {
      const tableComp = s.states.find((state) => state.componentName === 'table')
      if (tableComp) {
        const existingSheet = tableComp.sheetData?.[sheetKey] || {}

        // Merge new data with existing metadata
        tableComp.sheetData = {
          ...(tableComp.sheetData || {}),
          [sheetKey]: {
            ...existingSheet,
            ...newData,
            // Ensure table_id is preserved/updated
            table_id: newData.table_id || existingSheet.table_id,
            // Remove cleared flag
            isCleared: false,
            clearTimestamp: undefined,
          },
        }
      }
    })
  }

  function getTableId(sheetKey) {
    // Get table_id for a specific sheet
    const tableState = states.value.find((s) => s.componentName === 'table')
    if (!tableState || !tableState.sheetData || !tableState.sheetData[sheetKey]) {
      return null
    }
    return tableState.sheetData[sheetKey].table_id || null
  }

  // App information management functions
  function setAppInfo(info) {
    // Use reactive update to ensure UI reactivity
    this.$patch((s) => {
      s.appInfo = { ...s.appInfo, ...info }
    })
  }

  function addAppUpdate(update) {
    // Add new update to the beginning of the updates array
    this.$patch((s) => {
      s.appInfo.updates.unshift({
        ...update,
        id: Date.now(), // Simple ID generation
        date: update.date || new Date().toISOString().split('T')[0],
      })
      s.appInfo.hasNewUpdates = true
      s.appInfo.lastChecked = new Date().toISOString()
    })
  }

  function markUpdatesAsRead() {
    // Mark all updates as read
    this.$patch((s) => {
      s.appInfo.hasNewUpdates = false
    })
  }

  function checkForUpdates() {
    // Simulate checking for updates (in real app, this would call an API)
    this.$patch((s) => {
      s.appInfo.lastChecked = new Date().toISOString()
    })
  }

  return {
    pageName,
    title,
    taskName,
    setTaskName,
    clearTaskName,
    taskId,
    setTaskId,
    clearTaskId,
    states,
    stateIds,
    getStates,
    updateComponent,
    deleteComponent,
    chatMessages,
    setChatMessages,
    appendChatMessage,
    clearChatMessages,
    username,
    setUsername,
    clearUsername,
    userRole,
    setUserRole,
    clearUserRole,
    setAuthToken,
    clearAuthToken,
    tokenExpireTime,
    setTokenExpireTime,
    clearTokenExpireTime,
    userInfo,
    setUserInfo,
    clearUserInfo,
    clearAllAuthData,
    availableIndicators,
    setAvailableIndicators,
    clearAvailableIndicators,
    availableDimensions,
    setAvailableDimensions,
    clearAvailableDimensions,
    availableCategories,
    setAvailableCategories,
    clearAvailableCategories,
    selectedCategories,
    setSelectedCategories,
    clearSelectedCategories,
    systemError,
    showSystemError,
    hideSystemError,
    clearSystemError,
    clearSheetData,
    updateSheetData,
    getTableId,
    appInfo,
    setAppInfo,
    addAppUpdate,
    markUpdatesAsRead,
    checkForUpdates,
  }
})
