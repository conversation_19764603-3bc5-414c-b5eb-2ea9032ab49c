<template>
  <div v-if="show" class="notification-toast" :class="type">
    <span class="notification-icon">
      {{ getIcon(type) }}
    </span>
    <span class="notification-message">{{ message }}</span>
  </div>
</template>

<script setup>
import { watch, onMounted, onBeforeUnmount } from 'vue'
const props = defineProps({
  show: Boolean,
  message: String,
  type: {
    type: String,
    default: 'success', // 'success' | 'error' | 'info'
  },
  duration: {
    type: Number,
    default: 10000,
  },
})
const emit = defineEmits(['close'])

// Function to get appropriate icon based on type
const getIcon = (type) => {
  switch (type) {
    case 'success':
      return '✅'
    case 'error':
      return '❌'
    case 'info':
      return '🔄' // Refresh/reload icon
    default:
      return '❌'
  }
}
let timer = null

watch(
  () => props.show,
  (val) => {
    if (val) {
      clearTimeout(timer)
      timer = setTimeout(() => {
        emit('close')
      }, props.duration)
    } else {
      clearTimeout(timer)
    }
  },
)
onMounted(() => {
  if (props.show) {
    timer = setTimeout(() => {
      emit('close')
    }, props.duration)
  }
})
onBeforeUnmount(() => {
  clearTimeout(timer)
})
</script>

<style scoped>
.notification-toast {
  position: fixed;
  top: 18px; /* 20px * 0.9 */
  right: 18px; /* 20px * 0.9 */
  background-color: #fff;
  border-radius: 7.2px; /* 8px * 0.9 */
  padding: 10.8px 18px; /* 12px * 0.9, 20px * 0.9 */
  box-shadow: 0 3.6px 10.8px rgba(0, 0, 0, 0.15); /* 4px * 0.9, 12px * 0.9 */
  display: flex;
  align-items: center;
  gap: 7.2px; /* 8px * 0.9 */
  z-index: 9000;
  border-left: 3.6px solid #4caf50; /* 4px * 0.9 */
  animation: slideIn 0.3s ease;
}
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
.notification-icon {
  font-size: 16.2px; /* 18px * 0.9 */
}
.notification-message {
  font-size: 12.6px; /* 14px * 0.9 */
  color: #333;
  font-weight: 500;
}
.notification-toast.success {
  border-left-color: #4caf50;
}
.notification-toast.error {
  border-left-color: #f44336;
}
.notification-toast.info {
  border-left-color: #2196f3;
}
</style>
