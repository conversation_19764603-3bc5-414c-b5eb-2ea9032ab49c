<template>
  <div
    v-if="showPanel"
    class="app-info-panel"
    :class="{ minimized: isMinimized, dragging: isDragging }"
    :style="{ transform: `translate(${position.x}px, ${position.y}px)` }"
    @mousedown="startDrag"
  >
    <!-- Header -->
    <div class="panel-header" @mousedown="startDrag">
      <div class="header-left">
        <div class="app-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M12 2L2 7L12 12L22 7L12 2Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M2 17L12 22L22 17"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M2 12L12 17L22 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </div>
        <span class="panel-title">应用信息</span>
        <div v-if="appInfo.hasNewUpdates && !isMinimized" class="update-badge">
          {{ appInfo.updates.length }}
        </div>
      </div>
      <div class="header-buttons">
        <button
          class="header-btn minimize-btn"
          @click.stop="toggleMinimize"
          :title="isMinimized ? '展开' : '最小化'"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              v-if="!isMinimized"
              d="M18 12H6"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            />
            <path
              v-else
              d="M18 15L12 9L6 15"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <button class="header-btn close-btn" @click.stop="closePanel" title="关闭">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- Content -->
    <div v-if="!isMinimized" class="panel-content">
      <!-- Version Info -->
      <div class="version-section">
        <div class="version-info">
          <span class="version-label">当前版本</span>
          <span class="version-number">v{{ appInfo.version }}</span>
        </div>
        <div class="release-date">发布日期: {{ formatDate(appInfo.releaseDate) }}</div>
      </div>

      <!-- Updates Section -->
      <div class="updates-section">
        <div class="section-header">
          <h3 class="section-title">更新日志</h3>
          <button class="refresh-btn" @click="checkForUpdates" title="检查更新">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M1 4V10H7"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M23 20V14H17"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>

        <div class="updates-list">
          <div
            v-for="update in appInfo.updates.slice(0, 3)"
            :key="update.id"
            class="update-item"
            :class="{ latest: update.version === appInfo.version }"
          >
            <div class="update-header">
              <div class="update-version">
                <span class="version-tag">v{{ update.version }}</span>
                <span v-if="update.version === appInfo.version" class="current-tag">当前</span>
              </div>
              <span class="update-date">{{ formatDate(update.date) }}</span>
            </div>
            <div class="update-title">{{ update.title }}</div>
            <div class="update-description">{{ update.description }}</div>
            <div v-if="update.features && update.features.length > 0" class="update-features">
              <ul class="features-list">
                <li
                  v-for="feature in update.features.slice(0, 2)"
                  :key="feature"
                  class="feature-item"
                >
                  {{ feature }}
                </li>
                <li v-if="update.features.length > 2" class="feature-more">
                  +{{ update.features.length - 2 }} 更多功能
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div v-if="appInfo.updates.length > 3" class="view-all">
          <button class="view-all-btn" @click="showAllUpdates">
            查看全部更新 ({{ appInfo.updates.length }})
          </button>
        </div>
      </div>

      <!-- Footer -->
      <div class="panel-footer">
        <div class="last-checked">最后检查: {{ formatDateTime(appInfo.lastChecked) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useStates } from '@/store/states'
import { storeToRefs } from 'pinia'
import { fetchAppInfo, checkForUpdates as checkUpdatesAPI } from '@/utils/appInfoApi'

const statesStore = useStates()
const { appInfo } = storeToRefs(statesStore)

// Panel state - default to visible for first time users
const showPanel = ref(
  localStorage.getItem('appInfoPanelVisible') === null
    ? true
    : localStorage.getItem('appInfoPanelVisible') === 'true',
)
const isMinimized = ref(localStorage.getItem('appInfoPanelMinimized') === 'true')

// Dragging state
const isDragging = ref(false)
const position = ref({
  x: parseInt(localStorage.getItem('appInfoPanelX')) || window.innerWidth - 340,
  y: parseInt(localStorage.getItem('appInfoPanelY')) || window.innerHeight - 420,
})
const dragStart = ref({ x: 0, y: 0 })

// Watch for state changes and persist
const persistState = () => {
  localStorage.setItem('appInfoPanelVisible', showPanel.value.toString())
  localStorage.setItem('appInfoPanelMinimized', isMinimized.value.toString())
  localStorage.setItem('appInfoPanelX', position.value.x.toString())
  localStorage.setItem('appInfoPanelY', position.value.y.toString())
}

// Panel controls
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
  if (!isMinimized.value) {
    statesStore.markUpdatesAsRead()
  }
  persistState()
}

const closePanel = () => {
  showPanel.value = false
  persistState()
}

const showAllUpdates = () => {
  // In a real app, this might open a dedicated updates page
  alert('查看全部更新功能将在后续版本中实现')
}

const checkForUpdates = async () => {
  try {
    // Update last checked time immediately for UI feedback
    statesStore.checkForUpdates()

    // Check for updates via API
    const updatesInfo = await checkUpdatesAPI()

    if (updatesInfo.hasUpdates && updatesInfo.updates.length > 0) {
      // Add new updates to the store
      updatesInfo.updates.forEach((update) => {
        statesStore.addAppUpdate(update)
      })

      // Show notification about new updates
      if (window.showToast) {
        window.showToast(`发现 ${updatesInfo.updates.length} 个新更新`, 'info')
      }
    } else {
      // Show no updates message
      if (window.showToast) {
        window.showToast('当前已是最新版本', 'success')
      }
    }
  } catch (error) {
    console.error('Failed to check for updates:', error)
    if (window.showToast) {
      window.showToast('检查更新失败', 'error')
    }
  }
}

// Dragging functionality
const startDrag = (event) => {
  if (event.target.closest('.header-btn')) return

  isDragging.value = true
  dragStart.value = {
    x: event.clientX - position.value.x,
    y: event.clientY - position.value.y,
  }

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

const onDrag = (event) => {
  if (!isDragging.value) return

  const newX = event.clientX - dragStart.value.x
  const newY = event.clientY - dragStart.value.y

  // Constrain to viewport
  const maxX = window.innerWidth - 320 // panel width
  const maxY = window.innerHeight - (isMinimized.value ? 60 : 400) // panel height

  position.value = {
    x: Math.max(0, Math.min(newX, maxX)),
    y: Math.max(0, Math.min(newY, maxY)),
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  persistState()
}

// Utility functions
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// Initialize app info and position on mount
onMounted(async () => {
  // Load app info from API
  try {
    const appInfoData = await fetchAppInfo()
    statesStore.setAppInfo(appInfoData)
  } catch (error) {
    console.error('Failed to load app info on mount:', error)
  }

  // Mark updates as read when panel is opened and not minimized
  if (showPanel.value && !isMinimized.value) {
    statesStore.markUpdatesAsRead()
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
})

// Expose methods for parent component
defineExpose({
  showPanel: () => {
    showPanel.value = true
    persistState()
  },
  hidePanel: () => {
    showPanel.value = false
    persistState()
  },
  togglePanel: () => {
    showPanel.value = !showPanel.value
    persistState()
  },
})
</script>

<style scoped>
.app-info-panel {
  position: fixed;
  width: 320px;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 2500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  user-select: none;
}

.app-info-panel.minimized {
  height: 60px;
}

.app-info-panel.dragging {
  transition: none;
  cursor: grabbing;
}

/* Header */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px 12px 0 0;
  cursor: grab;
}

.panel-header:active {
  cursor: grabbing;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-icon {
  color: #1976d2;
  display: flex;
  align-items: center;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #1976d2;
}

.update-badge {
  background: #ff4444;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.header-buttons {
  display: flex;
  gap: 4px;
}

.header-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: all 0.2s ease;
}

.header-btn:hover {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

/* Content */
.panel-content {
  padding: 16px;
  max-height: 340px;
  overflow-y: auto;
}

.version-section {
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(25, 118, 210, 0.05);
  border-radius: 8px;
  border-left: 3px solid #1976d2;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.version-label {
  font-size: 12px;
  color: #666;
}

.version-number {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
}

.release-date {
  font-size: 11px;
  color: #888;
}

/* Updates Section */
.updates-section {
  margin-bottom: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.refresh-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  color: #666;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #1976d2;
}

.updates-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.update-item {
  padding: 12px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border-left: 3px solid #e0e0e0;
  transition: all 0.2s ease;
}

.update-item.latest {
  background: rgba(76, 175, 80, 0.05);
  border-left-color: #4caf50;
}

.update-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.update-version {
  display: flex;
  align-items: center;
  gap: 6px;
}

.version-tag {
  font-size: 11px;
  font-weight: 600;
  color: #1976d2;
  background: rgba(25, 118, 210, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.current-tag {
  font-size: 10px;
  font-weight: 600;
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

.update-date {
  font-size: 10px;
  color: #888;
}

.update-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.update-description {
  font-size: 11px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
}

.features-list {
  margin: 0;
  padding-left: 12px;
  list-style: none;
}

.feature-item {
  font-size: 10px;
  color: #666;
  margin-bottom: 2px;
  position: relative;
}

.feature-item::before {
  content: '•';
  color: #1976d2;
  position: absolute;
  left: -8px;
}

.feature-more {
  font-size: 10px;
  color: #888;
  font-style: italic;
}

.view-all {
  margin-top: 12px;
  text-align: center;
}

.view-all-btn {
  background: none;
  border: 1px solid #1976d2;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: #1976d2;
  color: white;
}

/* Footer */
.panel-footer {
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.last-checked {
  font-size: 10px;
  color: #999;
  text-align: center;
}

/* Scrollbar */
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
