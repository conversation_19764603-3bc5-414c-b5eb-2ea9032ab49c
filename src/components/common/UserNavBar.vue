<template>
  <nav class="user-nav-bar">
    <ul>
      <li :class="{ active: $route.path === '/user-center' }" @click="$router.push('/user-center')">
        <span class="nav-icon user"
          ><svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle
              cx="12"
              cy="8"
              r="4"
              :stroke="$route.path === '/user-center' ? '#1976d2' : '#8a8a8a'"
              stroke-width="2"
            />
            <path
              d="M4 20c0-4 4-6 8-6s8 2 8 6"
              :stroke="$route.path === '/user-center' ? '#1976d2' : '#8a8a8a'"
              stroke-width="2"
            /></svg
        ></span>
        <span>个人中心</span>
      </li>
      <li
        :class="{ active: $route.path === '/attribution-tasks' }"
        @click="$router.push('/attribution-tasks')"
      >
        <span class="nav-icon task"
          ><svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <rect
              x="4"
              y="4"
              width="16"
              height="16"
              rx="3"
              :stroke="$route.path === '/attribution-tasks' ? '#1976d2' : '#8a8a8a'"
              stroke-width="2"
            />
            <path
              d="M8 8h8M8 12h8M8 16h4"
              :stroke="$route.path === '/attribution-tasks' ? '#1976d2' : '#8a8a8a'"
              stroke-width="2"
            /></svg
        ></span>
        <span>归因任务管理</span>
      </li>
      <li>
        <span class="nav-icon approve"
          ><svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M12 20h9" stroke="#8a8a8a" stroke-width="2" />
            <rect
              x="3"
              y="4"
              width="18"
              height="12"
              rx="2"
              stroke="#8a8a8a"
              stroke-width="2"
            /></svg
        ></span>
        <span>审批管理</span>
      </li>
      <li>
        <span class="nav-icon knowledge"
          ><svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <rect x="4" y="4" width="16" height="16" rx="3" stroke="#8a8a8a" stroke-width="2" />
            <path d="M8 8h8M8 12h8M8 16h4" stroke="#8a8a8a" stroke-width="2" /></svg
        ></span>
        <span>知识库工作台</span>
      </li>
      <li
        v-if="userRole === '系统管理员'"
        :class="{ active: $route.path === '/admin' }"
        @click="$router.push('/admin')"
      >
        <span class="nav-icon admin">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <rect
              x="2"
              y="2"
              width="20"
              height="20"
              rx="5"
              :stroke="$route.path === '/admin' ? '#1976d2' : '#8a8a8a'"
              stroke-width="2"
            />
            <path
              d="M8 12h8M8 16h8M8 8h8"
              :stroke="$route.path === '/admin' ? '#1976d2' : '#8a8a8a'"
              stroke-width="2"
            />
          </svg>
        </span>
        <span>管理台</span>
      </li>
    </ul>
  </nav>
</template>

<script setup>
import { useStates } from '@/store/states'
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const statesStore = useStates()
const userRole = computed(() => statesStore.userRole)
const router = useRouter()
</script>

<style scoped>
.user-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 198px; /* 220px * 0.9 */
  height: 100vh;
  background: #fff;
  border-right: 1px solid #e6eaf0;
  box-sizing: border-box;
  padding-top: 54px; /* 60px * 0.9 */
  display: flex;
  flex-direction: column;
  align-items: stretch;
  z-index: 0;
}
.user-nav-bar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.user-nav-bar li {
  display: flex;
  align-items: center;
  gap: 10.8px; /* 12px * 0.9 */
  padding: 12.6px 28.8px; /* 14px * 0.9, 32px * 0.9 */
  font-size: 14.4px; /* 16px * 0.9 */
  color: #222;
  cursor: pointer;
  border-radius: 7.2px 0 0 7.2px; /* 8px * 0.9 */
  margin-bottom: 3.6px; /* 4px * 0.9 */
  transition:
    background 0.2s,
    color 0.2s;
}
.user-nav-bar li.active {
  background: #eaf3ff;
  color: #1976d2;
  font-weight: bold;
}
.user-nav-bar li.active .nav-icon svg {
  stroke: #1976d2;
}
.user-nav-bar li:not(.active):hover {
  background: #f5f7fa;
}
.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 21.6px; /* 24px * 0.9 */
  height: 21.6px; /* 24px * 0.9 */
}
/* Removed hardcoded user icon color - now dynamic based on route */
</style>
