<template>
  <div v-if="show" class="task-name-mask" @click.self="handleMaskClick">
    <div class="task-name-dialog">
      <!-- Header -->
      <div class="dialog-header">
        <h3 class="dialog-title">任务命名</h3>
        <button class="close-btn" @click="handleCancel" title="关闭">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="dialog-content">
        <p class="dialog-description">请为当前任务设置一个名称，便于后续查找和管理。</p>

        <div class="input-group">
          <label for="taskNameInput" class="input-label">任务名称</label>
          <input
            id="taskNameInput"
            ref="taskNameInput"
            v-model="inputValue"
            type="text"
            class="task-name-input"
            :class="{ 'input-error': hasError }"
            placeholder="请输入任务名称"
            maxlength="50"
            @keyup.enter="handleConfirm"
            @input="clearError"
          />
          <div v-if="hasError" class="error-message">{{ errorMessage }}</div>
          <div class="input-hint">最多50个字符</div>
        </div>
      </div>

      <!-- Actions -->
      <div class="dialog-actions">
        <button class="cancel-btn" @click="handleCancel">取消</button>
        <button class="confirm-btn" @click="handleConfirm" :disabled="isConfirming">
          <span v-if="isConfirming" class="loading-spinner"></span>
          {{ isConfirming ? '保存中...' : '确认' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  initialValue: {
    type: String,
    default: '个贷催收场景经营分析',
  },
})

const emit = defineEmits(['confirm', 'cancel', 'close'])

// Local state
const inputValue = ref('')
const hasError = ref(false)
const errorMessage = ref('')
const isConfirming = ref(false)
const taskNameInput = ref(null)

// Watch for show prop changes to reset state and focus input
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      inputValue.value = props.initialValue
      hasError.value = false
      errorMessage.value = ''
      isConfirming.value = false

      // Focus input after modal is shown
      nextTick(() => {
        if (taskNameInput.value) {
          taskNameInput.value.focus()
          taskNameInput.value.select()
        }
      })

      // Add escape key listener
      document.addEventListener('keydown', handleEscapeKey)
    } else {
      // Remove escape key listener
      document.removeEventListener('keydown', handleEscapeKey)
    }
  },
)

// Handle escape key
const handleEscapeKey = (event) => {
  if (event.key === 'Escape') {
    handleCancel()
  }
}

// Validation function
const validateInput = () => {
  const trimmedValue = inputValue.value.trim()

  if (!trimmedValue) {
    hasError.value = true
    errorMessage.value = '任务名称不能为空'
    return false
  }

  if (trimmedValue.length > 50) {
    hasError.value = true
    errorMessage.value = '任务名称不能超过50个字符'
    return false
  }

  return true
}

// Clear error state
const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

// Handle confirm action
const handleConfirm = async () => {
  if (!validateInput()) {
    return
  }

  isConfirming.value = true

  try {
    const trimmedValue = inputValue.value.trim()
    emit('confirm', trimmedValue)
  } catch (error) {
    console.error('Error confirming task name:', error)
    hasError.value = true
    errorMessage.value = '保存失败，请重试'
  } finally {
    isConfirming.value = false
  }
}

// Handle cancel action
const handleCancel = () => {
  emit('cancel')
}

// Handle mask click (background click)
const handleMaskClick = () => {
  // Allow dismissing on background click
  handleCancel()
}
</script>

<style scoped>
.task-name-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9998;
  backdrop-filter: blur(2.7px); /* 3px * 0.9 */
}

.task-name-dialog {
  background: white;
  border-radius: 10.8px; /* 12px * 0.9 */
  padding: 0;
  max-width: 432px; /* 480px * 0.9 */
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 14.4px 43.2px rgba(0, 0, 0, 0.2); /* 16px * 0.9, 48px * 0.9 */
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-18px); /* -20px * 0.9 */
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 21.6px; /* 20px * 0.9, 24px * 0.9 */
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.dialog-title {
  margin: 0;
  font-size: 1.125rem; /* 1.25rem * 0.9 */
  font-weight: 600;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  padding: 3.6px; /* 4px * 0.9 */
  cursor: pointer;
  color: #6b7280;
  border-radius: 3.6px; /* 4px * 0.9 */
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.dialog-content {
  padding: 21.6px; /* 24px * 0.9 */
}

.dialog-description {
  margin: 0 0 18px 0; /* 20px * 0.9 */
  color: #6b7280;
  font-size: 0.855rem; /* 0.95rem * 0.9 */
  line-height: 1.5;
}

.input-group {
  margin-bottom: 7.2px; /* 8px * 0.9 */
}

.input-label {
  display: block;
  margin-bottom: 7.2px; /* 8px * 0.9 */
  font-weight: 500;
  color: #374151;
  font-size: 0.855rem; /* 0.95rem * 0.9 */
}

.task-name-input {
  width: 100%;
  padding: 10.8px 14.4px; /* 12px * 0.9, 16px * 0.9 */
  border: 1.8px solid #d1d5db; /* 2px * 0.9 */
  border-radius: 7.2px; /* 8px * 0.9 */
  font-size: 0.9rem; /* 1rem * 0.9 */
  transition: all 0.2s;
  outline: none;
  box-sizing: border-box;
}

.task-name-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2.7px rgba(59, 130, 246, 0.1); /* 3px * 0.9 */
}

.task-name-input.input-error {
  border-color: #ef4444;
}

.task-name-input.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 2.7px rgba(239, 68, 68, 0.1); /* 3px * 0.9 */
}

.error-message {
  margin-top: 5.4px; /* 6px * 0.9 */
  color: #ef4444;
  font-size: 0.7875rem; /* 0.875rem * 0.9 */
}

.input-hint {
  margin-top: 5.4px; /* 6px * 0.9 */
  color: #9ca3af;
  font-size: 0.7875rem; /* 0.875rem * 0.9 */
}

.dialog-actions {
  display: flex;
  gap: 10.8px; /* 12px * 0.9 */
  padding: 18px 21.6px; /* 20px * 0.9, 24px * 0.9 */
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
  justify-content: flex-end;
}

.cancel-btn,
.confirm-btn {
  padding: 9px 18px; /* 10px * 0.9, 20px * 0.9 */
  border-radius: 5.4px; /* 6px * 0.9 */
  font-size: 0.855rem; /* 0.95rem * 0.9 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  gap: 5.4px; /* 6px * 0.9 */
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.cancel-btn:hover {
  background: #e5e7eb;
}

.confirm-btn {
  background: #3b82f6;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background: #2563eb;
}

.confirm-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.loading-spinner {
  width: 14.4px; /* 16px * 0.9 */
  height: 14.4px; /* 16px * 0.9 */
  border: 1.8px solid transparent; /* 2px * 0.9 */
  border-top: 1.8px solid currentColor; /* 2px * 0.9 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 576px) {
  /* 640px * 0.9 */
  .task-name-dialog {
    width: 95%;
    margin: 18px; /* 20px * 0.9 */
  }

  .dialog-header,
  .dialog-content,
  .dialog-actions {
    padding-left: 14.4px; /* 16px * 0.9 */
    padding-right: 14.4px; /* 16px * 0.9 */
  }

  .dialog-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .confirm-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
