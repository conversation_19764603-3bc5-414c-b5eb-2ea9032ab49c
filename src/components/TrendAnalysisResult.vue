<template>
  <div class="trend-result">
    <div class="result-header">
      <span class="result-title">📊 趋势分析结果</span>
      <span class="stationary-badge" :class="{ 'is-stationary': isStationary }">
        {{ isStationary ? '平稳' : '非平稳' }}
      </span>
    </div>
    <div v-if="isMulti">
      <div v-if="!useDropdown" class="target-switch-group">
        <button
          v-for="(item, idx) in targets"
          :key="item.target || item.result?.target || idx"
          :class="['target-switch-btn', { active: selectedIndex === idx }]"
          @click="selectedIndex = idx"
          type="button"
        >
          {{ item.target || item.result?.target || `目标${idx + 1}` }}
        </button>
      </div>
      <div v-else class="target-dropdown-group">
        <label class="target-dropdown-label">选择目标：</label>
        <div class="target-dropdown-box">
          <input
            v-if="useSearch"
            v-model="searchText"
            class="target-search-input"
            placeholder="搜索目标..."
            type="text"
          />
          <select v-model="selectedIndex" class="target-dropdown-select">
            <option v-for="(item, idx) in filteredTargets" :key="item.target || idx" :value="idx">
              {{ item.target || item.result?.target || `目标${idx + 1}` }}
            </option>
          </select>
        </div>
      </div>
      <div class="target-title">当前目标：{{ targetName }}</div>
    </div>
    <div class="result-grid">
      <!-- 月度趋势 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">📅</span>
          <span class="card-title">月度趋势</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">占比</span>
            <span class="metric-value">{{ formatPercentage(monthlyPct) }}</span>
          </div>
          <div class="strength-bar">
            <div
              class="strength-fill"
              :style="{ width: `${monthlyStrength * 100}%` }"
              :class="{ 'high-strength': monthlyStrength > 0.7 }"
            ></div>
          </div>
          <span class="strength-label">强度: {{ formatStrength(monthlyStrength) }}</span>
        </div>
      </div>

      <!-- 周度趋势 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">📆</span>
          <span class="card-title">周度趋势</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">占比</span>
            <span class="metric-value">{{ formatPercentage(weeklyPct) }}</span>
          </div>
          <div class="strength-bar">
            <div
              class="strength-fill"
              :style="{ width: `${weeklyStrength * 100}%` }"
              :class="{ 'high-strength': weeklyStrength > 0.7 }"
            ></div>
          </div>
          <span class="strength-label">强度: {{ formatStrength(weeklyStrength) }}</span>
        </div>
      </div>

      <!-- 长期趋势 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">📈</span>
          <span class="card-title">长期趋势</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">占比</span>
            <span class="metric-value">{{ formatPercentage(trendPct) }}</span>
          </div>
          <div class="strength-bar">
            <div
              class="strength-fill"
              :style="{ width: `${trendStrength * 100}%` }"
              :class="{ 'high-strength': trendStrength > 0.7 }"
            ></div>
          </div>
          <span class="strength-label">强度: {{ formatStrength(trendStrength) }}</span>
        </div>
      </div>

      <!-- 季节性趋势 -->
      <div class="result-card">
        <div class="card-header">
          <span class="card-icon">🔄</span>
          <span class="card-title">季节性趋势</span>
        </div>
        <div class="card-content">
          <div class="metric">
            <span class="metric-label">占比</span>
            <span class="metric-value">{{ formatPercentage(seasonalPct) }}</span>
          </div>
          <div class="strength-bar">
            <div
              class="strength-fill"
              :style="{ width: `${seasonalStrength * 100}%` }"
              :class="{ 'high-strength': seasonalStrength > 0.7 }"
            ></div>
          </div>
          <span class="strength-label">强度: {{ formatStrength(seasonalStrength) }}</span>
        </div>
      </div>
    </div>
    <div class="trend-view-btn-row">
      <button class="trend-view-btn" @click="showTrendChart = true">查看趋势图</button>
    </div>
    <div v-if="showTrendChart" class="trend-chart-modal-mask">
      <div class="trend-chart-modal-content">
        <button class="trend-chart-modal-close" @click="showTrendChart = false">×</button>
        <TrendChartModule
          v-if="trendSeries && trendSeries.length"
          :chartOptions="trendChartOptions"
        />
        <div v-else class="no-trend-data">暂无趋势数据可视化</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import TrendChartModule from './TrendChartModule.vue'

const props = defineProps({
  result: {
    type: [Object, Array],
    required: true,
    validator: (value) => {
      // Accepts: array of objects, or {status, result: {target: {...}}}
      if (Array.isArray(value)) {
        return value.every((v) => v && typeof v === 'object' && ('target' in v || v.target))
      }
      if (value && typeof value === 'object') {
        // Accepts {status, result: {...}} or {target: {...}}
        if ('status' in value && typeof value.result === 'object') return true
        if ('result' in value && typeof value.result === 'object') return true
        if (Object.keys(value).some((k) => typeof value[k] === 'object')) return true
        if ('target' in value) return true
      }
      return false
    },
  },
})

// Normalize input to array of {target, ...}
const normalizedTargets = computed(() => {
  // If backend format: {status, result: {target1: {...}, target2: {...}}}
  if (
    props.result &&
    typeof props.result === 'object' &&
    'result' in props.result &&
    typeof props.result.result === 'object'
  ) {
    return Object.entries(props.result.result).map(([target, res]) => ({ target, ...res }))
  }
  // If direct object: {target1: {...}, target2: {...}}
  if (
    props.result &&
    typeof props.result === 'object' &&
    !Array.isArray(props.result) &&
    !('target' in props.result)
  ) {
    return Object.entries(props.result).map(([target, res]) => ({ target, ...res }))
  }
  // If already array
  if (Array.isArray(props.result)) {
    return props.result
  }
  // If single object with target
  if (props.result && typeof props.result === 'object' && 'target' in props.result) {
    return [props.result]
  }
  return []
})

const isMulti = computed(() => normalizedTargets.value.length > 1)
const selectedIndex = ref(0)
const targets = normalizedTargets
const current = computed(() => targets.value[selectedIndex.value] || targets.value[0])

// Extract values from result (support both formats)
const isStationary = computed(
  () =>
    (current.value.result?.is_stationary || current.value.is_stationary) === 'Yes' ||
    current.value.is_stationary === 'Yes',
)
const monthlyPct = computed(() => current.value.result?.monthly_pct ?? current.value.monthly_pct)
const monthlyStrength = computed(
  () => current.value.result?.monthly_strength ?? current.value.monthly_strength,
)
const weeklyPct = computed(() => current.value.result?.weekly_pct ?? current.value.weekly_pct)
const weeklyStrength = computed(
  () => current.value.result?.weekly_strength ?? current.value.weekly_strength,
)
const trendPct = computed(() => current.value.result?.trend_pct ?? current.value.trend_pct)
const trendStrength = computed(
  () => current.value.result?.trend_strength ?? current.value.trend_strength,
)
const seasonalPct = computed(() => current.value.result?.seasonal_pct ?? current.value.seasonal_pct)
const seasonalStrength = computed(
  () => current.value.result?.seasonal_strength ?? current.value.seasonal_strength,
)
const targetName = computed(() => current.value.target || current.value.result?.target || '目标')

// Format helpers
const formatPercentage = (value) => `${(value * 100).toFixed(1)}%`
const formatStrength = (value) => {
  if (value === 0) return '无'
  if (value < 0.3) return '弱'
  if (value < 0.7) return '中'
  return '强'
}

const DROPDOWN_THRESHOLD = 5
const SEARCH_THRESHOLD = 10
const useDropdown = computed(() => targets.value.length > DROPDOWN_THRESHOLD)
const useSearch = computed(() => targets.value.length > SEARCH_THRESHOLD)
const searchText = ref('')
const filteredTargets = computed(() => {
  if (!useSearch.value || !searchText.value) return targets.value
  return targets.value.filter((t) =>
    (t.target || '').toLowerCase().includes(searchText.value.toLowerCase()),
  )
})

const showTrendChart = ref(false)
const trendSeries = computed(() => {
  // --- FAKE DATA FOR TESTING ---
  // Remove this block for production, keep the real access point below
  if (!(current.value.result?.trend_series || current.value.trend_series)) {
    // Example: 12 months
    return [
      { date: '2023-01', value: 120 },
      { date: '2023-02', value: 132 },
      { date: '2023-03', value: 101 },
      { date: '2023-04', value: 134 },
      { date: '2023-05', value: 90 },
      { date: '2023-06', value: 230 },
      { date: '2023-07', value: 210 },
      { date: '2023-08', value: 180 },
      { date: '2023-09', value: 160 },
      { date: '2023-10', value: 170 },
      { date: '2023-11', value: 190 },
      { date: '2023-12', value: 220 },
    ]
  }
  // --- END FAKE DATA ---
  return current.value.result?.trend_series || current.value.trend_series || []
})
const trendChartOptions = computed(() => {
  if (!trendSeries.value || !trendSeries.value.length) return {}
  return {
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: trendSeries.value.map((item) => item.date),
      boundaryGap: false,
      axisLabel: { color: '#1976d2' },
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#1976d2' },
      splitLine: { lineStyle: { color: '#e0e0e0' } },
    },
    series: [
      {
        name: '趋势值',
        type: 'line',
        data: trendSeries.value.map((item) => item.value),
        smooth: true,
        showSymbol: true,
        symbolSize: 7,
        lineStyle: { color: '#1976d2', width: 3 },
        itemStyle: { color: '#1976d2' },
        areaStyle: { color: 'rgba(25, 118, 210, 0.08)' },
      },
    ],
    grid: { left: 40, right: 20, top: 40, bottom: 40 },
  }
})
</script>

<style scoped>
.trend-result {
  background: #fff;
  border-radius: 10.8px; /* 12px * 0.9 */
  padding: 1.35rem; /* 1.5rem * 0.9 */
  box-shadow: 0 1.8px 7.2px rgba(0, 0, 0, 0.06); /* 2px * 0.9, 8px * 0.9 */
  width: 100%;
  min-width: 360px; /* 400px * 0.9 */
  max-width: 540px; /* 600px * 0.9 */
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.35rem; /* 1.5rem * 0.9 */
}

.result-title {
  font-size: 0.99rem; /* 1.1rem * 0.9 */
  font-weight: bold;
  color: #1976d2;
}

.stationary-badge {
  padding: 0.36rem 0.72rem; /* 0.4rem * 0.9, 0.8rem * 0.9 */
  border-radius: 18px; /* 20px * 0.9 */
  font-size: 0.81rem; /* 0.9rem * 0.9 */
  font-weight: 500;
  background: #ffebee;
  color: #d32f2f;
}

.stationary-badge.is-stationary {
  background: #e8f5e9;
  color: #2e7d32;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.9rem; /* 1rem * 0.9 */
}

.result-card {
  background: #f8f9fa;
  border-radius: 9px; /* 10px * 0.9 */
  padding: 0.9rem; /* 1rem * 0.9 */
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.result-card:hover {
  transform: translateY(-1.8px); /* -2px * 0.9 */
  box-shadow: 0 3.6px 10.8px rgba(0, 0, 0, 0.08); /* 4px * 0.9, 12px * 0.9 */
}

.card-header {
  display: flex;
  align-items: center;
  gap: 0.45rem; /* 0.5rem * 0.9 */
  margin-bottom: 0.9rem; /* 1rem * 0.9 */
}

.card-icon {
  font-size: 1.08rem; /* 1.2rem * 0.9 */
}

.card-title {
  font-weight: 500;
  color: #424242;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 0.45rem; /* 0.5rem * 0.9 */
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-label {
  color: #666;
  font-size: 0.81rem; /* 0.9rem * 0.9 */
}

.metric-value {
  font-weight: 500;
  color: #1976d2;
}

.strength-bar {
  height: 5.4px; /* 6px * 0.9 */
  background: #e0e0e0;
  border-radius: 2.7px; /* 3px * 0.9 */
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  background: #90caf9;
  border-radius: 2.7px; /* 3px * 0.9 */
  transition: width 0.3s ease;
}

.strength-fill.high-strength {
  background: #42b983;
}

.strength-label {
  font-size: 0.765rem; /* 0.85rem * 0.9 */
  color: #666;
  text-align: right;
}

@media (max-width: 691px) {
  /* 768px * 0.9 */
  .result-grid {
    grid-template-columns: 1fr;
  }
  .trend-result {
    min-width: 100%;
    max-width: 100%;
  }
}

.target-switch-group {
  display: flex;
  gap: 0;
  margin-bottom: 1.08rem; /* 1.2rem * 0.9 */
  border-radius: 7.2px; /* 8px * 0.9 */
  overflow: hidden;
  box-shadow: 0 1.8px 7.2px rgba(25, 118, 210, 0.06); /* 2px * 0.9, 8px * 0.9 */
  background: #e3eaf2;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}
.target-switch-btn {
  border: none;
  background: transparent;
  color: #1976d2;
  font-size: 0.9rem; /* 1rem * 0.9 */
  font-weight: 500;
  padding: 7.2px 19.8px 7.2px 16.2px; /* 8px * 0.9, 22px * 0.9, 18px * 0.9 */
  cursor: pointer;
  transition:
    background 0.18s,
    color 0.18s,
    box-shadow 0.18s;
  outline: none;
  display: flex;
  align-items: center;
  gap: 5.4px; /* 6px * 0.9 */
  border-right: 1px solid #d0d8e0;
}
.target-switch-btn:last-child {
  border-right: none;
}
.target-switch-btn.active {
  background: #1976d2;
  color: #fff;
  box-shadow: 0 1.8px 7.2px rgba(25, 118, 210, 0.13); /* 2px * 0.9, 8px * 0.9 */
  z-index: 1;
}
.target-switch-btn:hover:not(.active) {
  background: #f3f6fa;
  color: #1565c0;
}
.target-title {
  text-align: center;
  color: #1976d2;
  font-size: 0.945rem; /* 1.05rem * 0.9 */
  font-weight: 500;
  margin-bottom: 0.72rem; /* 0.8rem * 0.9 */
}

.target-dropdown-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.08rem; /* 1.2rem * 0.9 */
}
.target-dropdown-label {
  color: #1976d2;
  font-weight: 500;
  margin-bottom: 0.27rem; /* 0.3rem * 0.9 */
  font-size: 0.9rem; /* 1rem * 0.9 */
}
.target-dropdown-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.36rem; /* 0.4rem * 0.9 */
  width: 198px; /* 220px * 0.9 */
}
.target-dropdown-select {
  width: 100%;
  padding: 7.2px 10.8px; /* 8px * 0.9, 12px * 0.9 */
  border-radius: 5.4px; /* 6px * 0.9 */
  border: 1px solid #d0d8e0;
  font-size: 0.9rem; /* 1rem * 0.9 */
  color: #1976d2;
  background: #f8fafd;
  outline: none;
  transition: border 0.18s;
}
.target-dropdown-select:focus {
  border: 1.35px solid #1976d2; /* 1.5px * 0.9 */
}
.target-search-input {
  width: 100%;
  padding: 6.3px 9px; /* 7px * 0.9, 10px * 0.9 */
  border-radius: 5.4px; /* 6px * 0.9 */
  border: 1px solid #d0d8e0;
  font-size: 0.882rem; /* 0.98rem * 0.9 */
  color: #1976d2;
  background: #f3f6fa;
  outline: none;
  margin-bottom: 1.8px; /* 2px * 0.9 */
}
.target-search-input:focus {
  border: 1.35px solid #1976d2; /* 1.5px * 0.9 */
}

.trend-view-btn-row {
  display: flex;
  justify-content: center;
  margin-top: 1.35rem; /* 1.5rem * 0.9 */
}
.trend-view-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 5.4px; /* 6px * 0.9 */
  padding: 0.54rem 1.44rem; /* 0.6rem * 0.9, 1.6rem * 0.9 */
  font-size: 0.9rem; /* 1rem * 0.9 */
  font-weight: 500;
  cursor: pointer;
  transition: background 0.18s;
}
.trend-view-btn:hover {
  background: #1565c0;
}
.trend-chart-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.18);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.trend-chart-modal-content {
  background: #fff;
  border-radius: 10.8px; /* 12px * 0.9 */
  padding: 1.8rem 2.25rem 1.35rem 2.25rem; /* 2rem * 0.9, 2.5rem * 0.9, 1.5rem * 0.9 */
  box-shadow: 0 3.6px 21.6px rgba(0, 0, 0, 0.13); /* 4px * 0.9, 24px * 0.9 */
  min-width: 486px; /* 540px * 0.9 */
  min-height: 288px; /* 320px * 0.9 */
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.trend-chart-modal-close {
  position: absolute;
  right: 1.08rem; /* 1.2rem * 0.9 */
  top: 1.08rem; /* 1.2rem * 0.9 */
  background: none;
  border: none;
  font-size: 1.53rem; /* 1.7rem * 0.9 */
  color: #888;
  cursor: pointer;
  transition: color 0.18s;
}
.trend-chart-modal-close:hover {
  color: #1976d2;
}
.no-trend-data {
  color: #888;
  font-size: 0.99rem; /* 1.1rem * 0.9 */
  margin-top: 1.8rem; /* 2rem * 0.9 */
}
</style>
