<template>
  <header class="header-bar">
    <div class="header-left">
      <button @click="navigateToHome" class="home-btn">
        <svg class="home-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path
            d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <polyline
            points="9,22 9,12 15,12 15,22"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
        <span>主页</span>
      </button>
      <button @click="toggleHistoryPanel" class="history-btn">
        <svg class="history-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path
            d="M12 8v4l3 3"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" />
        </svg>
        <span>历史任务</span>
      </button>
    </div>
    <div class="header-title">
      <div class="title-main">{{ displayTitle }}</div>
    </div>
    <div class="header-actions">
      <button
        class="action-btn"
        v-if="pageName == '引导式'"
        @click="handleSaveTask"
        :disabled="isLoading"
      >
        {{ isLoading ? '保存中...' : '保存任务' }}
      </button>
      <button class="user-avatar-btn" @click="navigateToUserCenter">
        <img class="header-user-avatar" src="https://ui-avatars.com/api/?name=U" alt="User" />
      </button>
    </div>
  </header>

  <!-- Task Name Modal -->
  <TaskNameModal
    :show="showTaskNameModal"
    :initial-value="taskName"
    @confirm="handleTaskNameConfirm"
    @cancel="handleTaskNameCancel"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { collectPageState, applyPageState } from '../utils/pageState'
import { useStates } from '../store/states'
import { storeToRefs } from 'pinia'
import TaskNameModal from './common/TaskNameModal.vue'
import axios from 'axios'

const emit = defineEmits(['toggle-history'])

const statesStore = useStates()
const { pageName, title, taskName } = storeToRefs(statesStore)
const router = useRouter()
const route = useRoute()

const isLoading = ref(false)
const showTaskNameModal = ref(false)

// Compute the display title based on current route and page type
const displayTitle = computed(() => {
  if (route.path === '/') {
    return '智能统一归因平台'
  }

  // For guided analysis pages, use task name as the main title
  if (pageName.value === '引导式') {
    return taskName.value || '个贷催收场景经营分析'
  }

  return title.value || '智能统一归因平台'
})

const navigateToHome = () => {
  router.push('/')
}

const toggleHistoryPanel = () => {
  // Emit event to parent component to toggle history panel
  emit('toggle-history')
}

const handleSaveTask = () => {
  // Show task name modal before saving
  showTaskNameModal.value = true
}

const handleTaskNameConfirm = async (newTaskName) => {
  try {
    isLoading.value = true

    // Update task name in store
    statesStore.setTaskName(newTaskName)

    // Call the create_task API endpoint
    const response = await axios.post(
      '/api/v1/task/create_task',
      {
        task_name: newTaskName,
        task_type: pageName.value,
      },
      {
        headers: {
          token: localStorage.getItem('token'),
          'Content-Type': 'application/json',
        },
      },
    )

    console.log('Create task API response:', response.data)

    // Handle API response
    if (response.data.error_code === 0) {
      // Success - extract task_id and update store
      const taskId = response.data.data?.task_id
      if (taskId) {
        statesStore.setTaskId(taskId)
        console.log('Task ID saved to store:', taskId)
      }

      alert(`任务 "${newTaskName}" 保存成功`)
      showTaskNameModal.value = false
    } else {
      // API returned error
      const errorMessage = response.data.message || '保存失败'
      throw new Error(errorMessage)
    }
  } catch (error) {
    console.error('保存失败:', error)

    // Handle different types of errors
    let errorMessage = '保存失败'
    if (error.response) {
      // Server responded with error status
      const errorData = error.response.data
      if (errorData.error_code !== 0) {
        errorMessage = errorData.message || '保存失败，请重试'
      }
    } else if (error.request) {
      // Network error
      errorMessage = '网络连接失败，请检查网络设置'
    } else {
      // Other error
      errorMessage = error.message || '保存失败，请重试'
    }

    alert('保存失败: ' + errorMessage)
  } finally {
    isLoading.value = false
  }
}

const handleTaskNameCancel = () => {
  showTaskNameModal.value = false
}

const handleLoadAll = async () => {
  try {
    isLoading.value = true

    const response = await fetch('http://localhost:5000/load_allinfo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error('加载失败')
    }

    const result = await response.json()
    applyPageState(result.allinfo)
    alert('加载成功')
  } catch (error) {
    console.error('加载失败:', error)
    alert('加载失败: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const navigateToUserCenter = () => {
  router.push('/user-center')
}
</script>

<style scoped>
.header-bar {
  width: 100%;
  min-height: 50px; /* 64px * 0.9 */
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky; /* changed from relative to sticky */
  top: 0; /* stick to the top */
  box-shadow: 0 1.8px 7.2px rgba(0, 0, 0, 0.03); /* 2px * 0.9, 8px * 0.9 */
  z-index: 1000; /* increased to ensure it stays above other content */
  padding: 0 18px; /* 32px * 0.9 */
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10.8px; /* 12px * 0.9 */
}

.home-btn {
  display: flex;
  align-items: center;
  gap: 7.2px; /* 8px * 0.9 */
  padding: 5.2px 14.4px; /* 8px * 0.9, 16px * 0.9 */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5.4px; /* 6px * 0.9 */
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px; /* 14px * 0.9 */
  font-weight: 500;
  color: #333;
  box-shadow: 0 0.9px 2.7px rgba(0, 0, 0, 0.1); /* 1px * 0.9, 3px * 0.9 */
}

.home-btn:hover {
  background: #f5f5f5;
  transform: translateY(-0.9px); /* -1px * 0.9 */
  box-shadow: 0 1.8px 5.4px rgba(0, 0, 0, 0.15); /* 2px * 0.9, 6px * 0.9 */
}

.home-icon {
  color: #0a58ca;
}

.history-btn {
  display: flex;
  align-items: center;
  gap: 7.2px; /* 8px * 0.9 */
  padding: 5.2px 14.4px; /* 8px * 0.9, 16px * 0.9 */
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 5.4px; /* 6px * 0.9 */
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px; /* 14px * 0.9 */
  font-weight: 500;
  color: #333;
  box-shadow: 0 0.9px 2.7px rgba(0, 0, 0, 0.1); /* 1px * 0.9, 3px * 0.9 */
}

.history-btn:hover {
  background: #f5f5f5;
  transform: translateY(-0.9px); /* -1px * 0.9 */
  box-shadow: 0 1.8px 5.4px rgba(0, 0, 0, 0.15); /* 2px * 0.9, 6px * 0.9 */
}

.history-icon {
  color: #0a58ca;
}

.header-title {
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.title-main {
  font-size: 1.2rem; /* 1.5rem * 0.9 */
  font-weight: bold;
  color: #222;
  letter-spacing: 1.8px; /* 2px * 0.9 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.9rem; /* 1rem * 0.9 */
}

.action-btn {
  padding: 7.2px 14.4px; /* 8px * 0.9, 16px * 0.9 */
  background: #0d6efd;
  color: white;
  border: none;
  border-radius: 3.6px; /* 4px * 0.9 */
  cursor: pointer;
  font-size: 12.6px; /* 14px * 0.9 */
  transition: all 0.2s;
}
.action-btn:hover:not(:disabled) {
  background: #1565c0;
}
.action-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.user-avatar-btn {
  background: none;
  border: none;
  padding: 0;
  margin-left: 10.8px; /* 12px * 0.9 */
  cursor: pointer;
  display: flex;
  align-items: center;
}
.header-user-avatar {
  width: 32.4px; /* 36px * 0.9 */
  height: 32.4px; /* 36px * 0.9 */
  border-radius: 50%;
  border: 1.8px solid #e0e0e0; /* 2px * 0.9 */
  background: #fafafa;
}
</style>
