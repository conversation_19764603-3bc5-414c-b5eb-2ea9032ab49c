<script setup>
import { ref, computed, watch } from 'vue'
import { useStates } from '@/store/states'

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['close'])

const statesStore = useStates()
const tableState = computed(() => statesStore.states.find((s) => s.componentName === 'table'))
const sheetData = computed(() => tableState.value?.sheetData || {})
const activeSheetKey = computed(() => tableState.value?.activeSheet || '')

// Data quality analysis
const qualityMetrics = ref({
  totalRows: 0,
  totalColumns: 0,
  missingValues: 0,
  duplicateRows: 0,
  dataTypes: {},
  columnQuality: [],
  totalOutliers: 0,
  isUnavailable: false, // Flag to indicate if enhanced metrics are unavailable
  qualityScore: 0, // API-provided overall quality score
  overallMissingRate: 0, // Overall missing rate as percentage
  overallDirtyRate: 0, // Overall dirty rate as percentage
})

const hasShownForCurrentData = ref(false)

// Define hasData computed property first
const hasData = computed(() => {
  const currentData = sheetData.value[activeSheetKey.value]
  return (
    currentData &&
    Array.isArray(currentData.rows) &&
    currentData.rows.length > 0 &&
    Array.isArray(currentData.columns) &&
    currentData.columns.length > 0
  )
})

// Analyze data quality using enhanced table_info structure only
const analyzeDataQuality = () => {
  try {
    const currentData = sheetData.value[activeSheetKey.value]
    if (!currentData || !currentData.rows || !currentData.columns) {
      // Reset metrics when no data
      resetQualityMetrics()
      return
    }

    const rows = currentData.rows
    const columns = currentData.columns
    const tableInfo = currentData.table_info

    // Only use enhanced table_info with pre-calculated metrics
    if (tableInfo && tableInfo.output_info_dict && tableInfo.output_df_dict) {
      analyzeWithEnhancedTableInfo(rows, columns, tableInfo)
    } else {
      // No enhanced table_info available - show unavailable state
      setMetricsUnavailable(rows, columns)
    }
  } catch (error) {
    console.error('Error analyzing data quality:', error)
    resetQualityMetrics()
  }
}

// Analyze using enhanced table_info structure from API
const analyzeWithEnhancedTableInfo = (rows, columns, tableInfo) => {
  const { output_info_dict, output_df_dict, output_type_dict } = tableInfo

  // Basic metrics from API
  qualityMetrics.value.totalRows = output_info_dict.total_rows || rows.length
  qualityMetrics.value.totalColumns = output_info_dict.total_columns || columns.length
  qualityMetrics.value.duplicateRows = output_info_dict.duplicate_rows || 0
  qualityMetrics.value.qualityScore = output_info_dict.quality_score || 0
  qualityMetrics.value.isUnavailable = false

  // Calculate missing values and build column quality from API data
  let totalMissingValues = 0
  const columnQuality = []

  columns.forEach((col) => {
    const columnMeta = output_df_dict[col.field] || {}
    const missingCount = columnMeta.missing_count || 0
    const missingRate = columnMeta.missing_rate || 0
    const dirtyCount = columnMeta.dirty_count || 0
    const dirtyRate = columnMeta.dirty_rate || 0

    totalMissingValues += missingCount

    // Map API data types to our internal types
    const apiDataType = columnMeta.dtype || 'unknown'
    const dataType = mapApiDataType(apiDataType)

    // Calculate temporary column quality score based on missing and dirty rates
    // TODO: Replace with backend-provided column quality scores when available
    // Formula: quality_score = 100 - (missing_rate * 80 + dirty_rate * 20)
    const tempQualityScore = Math.max(0, 100 - (missingRate * 80 + dirtyRate * 20))
    const qualityLevel = getQualityLevelFromScore(tempQualityScore)

    columnQuality.push({
      field: col.field,
      title: col.title,
      missingCount,
      missingPercentage: parseFloat((missingRate * 100).toFixed(1)),
      dirtyCount,
      dirtyPercentage: parseFloat((dirtyRate * 100).toFixed(1)),
      dataType,
      quality: qualityLevel, // Use calculated quality level for icon/color
      qualityScore: tempQualityScore, // Store numeric score for future use
      outlierCount: col.outlier_count || 0, // Keep existing outlier count if available
    })
  })

  qualityMetrics.value.missingValues = totalMissingValues
  qualityMetrics.value.totalOutliers = columnQuality.reduce((sum, col) => sum + col.outlierCount, 0)

  // Store overall rates as percentages for display
  qualityMetrics.value.overallMissingRate = parseFloat(
    ((output_info_dict.overall_missing_rate || 0) * 100).toFixed(1),
  )
  qualityMetrics.value.overallDirtyRate = parseFloat(
    ((output_info_dict.overall_dirty_rate || 0) * 100).toFixed(1),
  )

  // Data types analysis from API
  const dataTypes = {}
  if (output_type_dict) {
    dataTypes.numeric = (output_type_dict.float_count || 0) + (output_type_dict.int_count || 0)
    dataTypes.text = output_type_dict.string_count || 0
    // Add other types as needed
  } else {
    // Fallback to column-based counting
    columns.forEach((col) => {
      const columnMeta = output_df_dict[col.field] || {}
      const apiDataType = columnMeta.dtype || 'unknown'
      const dataType = mapApiDataType(apiDataType)
      dataTypes[dataType] = (dataTypes[dataType] || 0) + 1
    })
  }

  qualityMetrics.value.dataTypes = dataTypes
  qualityMetrics.value.columnQuality = columnQuality
}

// Reset quality metrics to default state
const resetQualityMetrics = () => {
  qualityMetrics.value = {
    totalRows: 0,
    totalColumns: 0,
    missingValues: 0,
    duplicateRows: 0,
    dataTypes: {},
    columnQuality: [],
    totalOutliers: 0,
    isUnavailable: false,
    qualityScore: 0,
    overallMissingRate: 0,
    overallDirtyRate: 0,
  }
}

// Set metrics unavailable state when enhanced table_info is not present
const setMetricsUnavailable = (rows, columns) => {
  qualityMetrics.value = {
    totalRows: rows.length,
    totalColumns: columns.length,
    missingValues: 0,
    duplicateRows: 0,
    dataTypes: {},
    columnQuality: [],
    totalOutliers: 0,
    isUnavailable: true, // Flag to indicate metrics are unavailable
    qualityScore: 0,
    overallMissingRate: 0,
    overallDirtyRate: 0,
  }
}

// Map API data types to internal data types
const mapApiDataType = (apiDataType) => {
  switch (apiDataType.toLowerCase()) {
    case 'int':
    case 'float':
    case 'number':
      return 'numeric'
    case 'string':
    case 'str':
    case 'text':
      return 'text'
    case 'datetime':
    case 'date':
      return 'date'
    case 'bool':
    case 'boolean':
      return 'boolean'
    default:
      return 'unknown'
  }
}

// Convert numeric quality score to quality level for icon/color display
const getQualityLevelFromScore = (score) => {
  if (score >= 90) return 'excellent'
  if (score >= 70) return 'good'
  if (score >= 50) return 'fair'
  return 'poor'
}

// Get quality color
const getQualityColor = (quality) => {
  const colors = {
    excellent: '#4caf50',
    good: '#8bc34a',
    fair: '#ff9800',
    poor: '#f44336',
  }
  return colors[quality] || '#999'
}

// Get quality icon
const getQualityIcon = (quality) => {
  const icons = {
    excellent: '🟢',
    good: '🟡',
    fair: '🟠',
    poor: '🔴',
  }
  return icons[quality] || '⚪'
}

// Overall data quality score - use API-provided score when available
const overallQualityScore = computed(() => {
  // Use API-provided quality score if available
  if (qualityMetrics.value.qualityScore > 0) {
    return qualityMetrics.value.qualityScore
  }

  // Return 0 if metrics are unavailable
  if (qualityMetrics.value.isUnavailable) {
    return 0
  }

  // Fallback calculation (should rarely be used with enhanced API)
  if (qualityMetrics.value.columnQuality.length === 0) return 0

  const avgMissing =
    qualityMetrics.value.columnQuality.reduce((sum, col) => sum + (col.missingPercentage || 0), 0) /
    qualityMetrics.value.columnQuality.length
  return Math.round((1 - avgMissing / 100) * 100)
})

const overallQualityLevel = computed(() => {
  if (overallQualityScore.value >= 90) return 'excellent'
  if (overallQualityScore.value >= 70) return 'good'
  if (overallQualityScore.value >= 50) return 'fair'
  return 'poor'
})

// Watch for data changes (auto-popup on new data)
watch(
  hasData,
  (newHasData, oldHasData) => {
    if (newHasData && !oldHasData && !hasShownForCurrentData.value) {
      setTimeout(() => {
        analyzeDataQuality()
        emit('close') // close first if open
        emit('close') // double close to ensure
        hasShownForCurrentData.value = true
        // Parent will set show=true to open
      }, 100)
    }
  },
  { immediate: true },
)

// Watch for show prop to trigger analysis when opened
watch(
  () => props.show,
  (newShow) => {
    if (newShow && hasData.value) {
      analyzeDataQuality()
    }
  },
)

const closeQualityPanel = () => {
  emit('close')
}

// Reset quality analysis state
const resetQualityState = () => {
  hasShownForCurrentData.value = false
  // isVisible.value = false // This line is removed
}

// Expose reset method
defineExpose({
  resetQualityState,
})

// Format numbers
const formatNumber = (num) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M'
  if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
  return num.toString()
}
</script>

<template>
  <div v-if="props.show" class="data-quality-overlay">
    <div class="data-quality-panel">
      <!-- Header -->
      <div class="quality-header">
        <div class="quality-header-left">
          <div class="quality-icon">📊</div>
          <div class="quality-title">
            <h3>数据质量分析</h3>
            <p class="quality-subtitle">数据加载完成，发现以下质量指标</p>
          </div>
        </div>
        <button class="quality-close-btn" @click="closeQualityPanel">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
            />
          </svg>
        </button>
      </div>

      <!-- Enhanced Metrics Unavailable Message -->
      <div v-if="qualityMetrics.isUnavailable" class="metrics-unavailable">
        <div class="unavailable-card">
          <div class="unavailable-icon">⚠️</div>
          <div class="unavailable-content">
            <h4>数据质量指标不可用</h4>
            <p>
              当前表格缺少详细的数据质量信息。要获取完整的质量分析，请使用支持增强质量分析的数据源。
            </p>
            <div class="basic-info">
              <span
                >基本信息：{{ qualityMetrics.totalRows }} 行，{{
                  qualityMetrics.totalColumns
                }}
                列</span
              >
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Quality Analysis (only show when available) -->
      <div v-else>
        <!-- Overall Quality Score -->
        <div class="overall-quality">
          <div class="quality-score-card">
            <div class="score-circle" :style="{ '--score': overallQualityScore }">
              <div class="score-number">{{ overallQualityScore }}</div>
              <div class="score-label">总体质量</div>
            </div>
            <div class="score-details">
              <div class="score-level" :style="{ color: getQualityColor(overallQualityLevel) }">
                {{ getQualityIcon(overallQualityLevel) }}
                {{
                  overallQualityLevel === 'excellent'
                    ? '优秀'
                    : overallQualityLevel === 'good'
                      ? '良好'
                      : overallQualityLevel === 'fair'
                        ? '一般'
                        : '较差'
                }}
              </div>
              <div class="score-description">
                {{ qualityMetrics.totalRows }} 行数据，{{ qualityMetrics.totalColumns }} 个字段
              </div>
            </div>
          </div>
        </div>

        <!-- Key Metrics -->
        <div class="quality-metrics">
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-icon">📈</div>
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(qualityMetrics.totalRows) }}</div>
                <div class="metric-label">总行数</div>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">📋</div>
              <div class="metric-content">
                <div class="metric-value">{{ qualityMetrics.totalColumns }}</div>
                <div class="metric-label">总列数</div>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">⚠️</div>
              <div class="metric-content">
                <div class="metric-value">{{ qualityMetrics.overallMissingRate }}%</div>
                <div class="metric-label">总体缺失率</div>
              </div>
            </div>

            <!-- Dirty data metric card -->
            <div class="metric-card">
              <div class="metric-icon">🚩</div>
              <div class="metric-content">
                <div class="metric-value">{{ qualityMetrics.overallDirtyRate }}%</div>
                <div class="metric-label">脏数据率</div>
              </div>
            </div>

            <div class="metric-card">
              <div class="metric-icon">🔄</div>
              <div class="metric-content">
                <div class="metric-value">{{ formatNumber(qualityMetrics.duplicateRows) }}</div>
                <div class="metric-label">重复行</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Column Quality Details -->
        <div class="column-quality-section">
          <h4 class="section-title">字段质量详情</h4>
          <div class="column-quality-list">
            <div
              v-for="column in qualityMetrics.columnQuality"
              :key="column.field"
              class="column-quality-item"
            >
              <div class="column-info">
                <div class="column-name">{{ column.title }}</div>
                <div class="column-type">
                  {{
                    column.dataType === 'numeric'
                      ? '数值型'
                      : column.dataType === 'date'
                        ? '日期型'
                        : column.dataType === 'text'
                          ? '文本型'
                          : '未知'
                  }}
                </div>
              </div>

              <div class="column-stats">
                <div class="missing-info">
                  <span class="missing-percentage">{{ column.missingPercentage }}%</span>
                  <span class="missing-label">缺失</span>
                </div>
                <!-- Show dirty data info when available -->
                <div v-if="column.dirtyCount !== undefined" class="dirty-info">
                  <span class="dirty-percentage">{{ column.dirtyPercentage }}%</span>
                  <span class="dirty-label">脏数据</span>
                </div>
                <div class="quality-indicator" :style="{ color: getQualityColor(column.quality) }">
                  {{ getQualityIcon(column.quality) }}
                </div>
                <!-- Outlier count per column (legacy support) -->
                <div
                  v-if="column.dataType === 'numeric' && column.outlierCount > 0"
                  class="outlier-info"
                >
                  🚩异常值: {{ column.outlierCount }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Data Type Distribution -->
        <div class="data-type-section" v-if="Object.keys(qualityMetrics.dataTypes).length > 0">
          <h4 class="section-title">数据类型分布</h4>
          <div class="data-type-grid">
            <div
              v-for="(count, type) in qualityMetrics.dataTypes"
              :key="type"
              class="data-type-item"
            >
              <div class="type-icon">
                {{
                  type === 'numeric' ? '🔢' : type === 'date' ? '📅' : type === 'text' ? '📝' : '❓'
                }}
              </div>
              <div class="type-info">
                <div class="type-name">
                  {{
                    type === 'numeric'
                      ? '数值型'
                      : type === 'date'
                        ? '日期型'
                        : type === 'text'
                          ? '文本型'
                          : '未知'
                  }}
                </div>
                <div class="type-count">{{ count }} 个字段</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- End of enhanced quality analysis section -->

      <!-- Action Buttons -->
      <div class="quality-actions">
        <button class="action-btn secondary" @click="closeQualityPanel">稍后查看</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.data-quality-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.08);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.data-quality-panel {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quality-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 32px 20px 32px;
  border-bottom: 1px solid #e0e0e0;
}

.quality-header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.quality-icon {
  font-size: 32px;
  margin-top: 4px;
}

.quality-title h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1976d2;
}

.quality-subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.quality-close-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quality-close-btn:hover {
  background: #f5f5f5;
  color: #666;
}

.overall-quality {
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafd 0%, #e3f2fd 100%);
}

.quality-score-card {
  display: flex;
  align-items: center;
  gap: 24px;
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.08);
}

.score-circle {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(#4caf50 calc(var(--score) * 1%), #e0e0e0 calc(var(--score) * 1%));
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-circle::before {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #fff;
}

.score-number {
  position: relative;
  font-size: 20px;
  font-weight: 700;
  color: #1976d2;
}

.score-label {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.score-details {
  flex: 1;
}

.score-level {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.score-description {
  color: #666;
  font-size: 14px;
}

.quality-metrics {
  padding: 24px 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.metric-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafd;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e0e0e0;
}

.metric-icon {
  font-size: 24px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 700;
  color: #1976d2;
  margin-bottom: 2px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.column-quality-section {
  padding: 0 32px 24px 32px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  border-bottom: 2px solid #e3f2fd;
  padding-bottom: 8px;
}

.column-quality-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.column-quality-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafd;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.column-info {
  flex: 1;
}

.column-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.column-type {
  font-size: 12px;
  color: #666;
}

.column-stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

.missing-info {
  text-align: right;
}

.missing-count {
  font-weight: 600;
  color: #f44336;
}

.missing-percentage {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

.quality-indicator {
  font-size: 16px;
}

.data-type-section {
  padding: 0 32px 24px 32px;
}

.data-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 12px;
}

.data-type-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafd;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.type-icon {
  font-size: 20px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.type-count {
  font-size: 12px;
  color: #666;
}

.quality-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 14px 32px;
  border-top: 1px solid #e0e0e0;
  background: #f8fafd;
}

.action-btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s;
}

.action-btn.secondary {
  background: #f5f7fa;
  color: #1976d2;
}

.action-btn.secondary:hover {
  background: #e3f2fd;
}

.action-btn.primary {
  background: #1976d2;
  color: #fff;
}

.action-btn.primary:hover {
  background: #1565c0;
}

/* Metrics unavailable styles */
.metrics-unavailable {
  padding: 20px 24px;
}

.unavailable-card {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
}

.unavailable-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.unavailable-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #856404;
}

.unavailable-content p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #856404;
  line-height: 1.5;
}

.basic-info {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

/* Enhanced column stats styles */
.column-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.missing-info,
.dirty-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.missing-info {
  color: #ff9800;
}

.dirty-info {
  color: #f44336;
}

.missing-label,
.dirty-label {
  font-size: 11px;
  opacity: 0.8;
}

.outlier-info {
  font-size: 13px;
  color: #f44336;
}

@media (max-width: 691px) {
  /* 768px * 0.9 */
  .data-quality-panel {
    margin: 9px; /* 10px * 0.9 */
    max-height: 95vh;
  }

  .quality-header {
    padding: 18px 21.6px 14.4px 21.6px; /* 20px * 0.9, 24px * 0.9, 16px * 0.9 */
  }

  .overall-quality {
    padding: 18px 21.6px; /* 20px * 0.9, 24px * 0.9 */
  }

  .quality-metrics {
    padding: 18px 21.6px; /* 20px * 0.9, 24px * 0.9 */
  }

  .column-quality-section,
  .data-type-section {
    padding: 0 21.6px 18px 21.6px; /* 24px * 0.9, 20px * 0.9 */
  }

  .quality-actions {
    padding: 18px 21.6px; /* 20px * 0.9, 24px * 0.9 */
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .data-type-grid {
    grid-template-columns: 1fr;
  }
}
</style>
