/**
 * Authentication utilities for token validation and user authentication status
 */

/**
 * Get authentication token from localStorage
 * @returns {string|null} Authentication token or null if not found
 */
export function getAuthToken() {
  // Check both possible token keys for backward compatibility
  return localStorage.getItem('token') || localStorage.getItem('auth_token') || null
}

/**
 * Get token expiration time from localStorage
 * @returns {string|null} Token expiration time or null if not found
 */
export function getTokenExpireTime() {
  return localStorage.getItem('token_expire_time') || null
}

/**
 * Get user info from localStorage
 * @returns {Object|null} User info object or null if not found
 */
export function getUserInfo() {
  try {
    const userInfoStr = localStorage.getItem('user_info')
    return userInfoStr ? JSON.parse(userInfoStr) : null
  } catch (error) {
    console.error('Failed to parse user info from localStorage:', error)
    return null
  }
}

/**
 * Check if authentication token exists
 * @returns {boolean} True if token exists, false otherwise
 */
export function hasAuthToken() {
  const token = getAuthToken()
  return token !== null && token !== undefined && token.trim() !== ''
}

/**
 * Check if authentication token is expired
 * @returns {boolean} True if token is expired, false otherwise
 */
export function isTokenExpired() {
  const expireTime = getTokenExpireTime()

  if (!expireTime) {
    // If no expiration time is set, consider token as expired
    return true
  }

  try {
    // Parse expiration time - assuming it's in ISO format or timestamp
    let expireTimestamp

    if (typeof expireTime === 'string') {
      // Try parsing as ISO date first
      if (expireTime.includes('T') || expireTime.includes('-')) {
        expireTimestamp = new Date(expireTime).getTime()
      } else {
        // Try parsing as timestamp (seconds or milliseconds)
        const numericTime = parseInt(expireTime, 10)
        // If it's in seconds (typical Unix timestamp), convert to milliseconds
        expireTimestamp = numericTime < 10000000000 ? numericTime * 1000 : numericTime
      }
    } else {
      expireTimestamp = expireTime
    }

    if (isNaN(expireTimestamp)) {
      console.warn('Invalid token expiration time format:', expireTime)
      return true
    }

    const currentTime = Date.now()
    const isExpired = currentTime >= expireTimestamp

    if (isExpired) {
      console.log('Token has expired:', {
        currentTime: new Date(currentTime).toISOString(),
        expireTime: new Date(expireTimestamp).toISOString(),
      })
    }

    return isExpired
  } catch (error) {
    console.error('Error checking token expiration:', error)
    return true // Consider expired if we can't parse the time
  }
}

/**
 * Check if user is authenticated (has valid, non-expired token)
 * @returns {boolean} True if user is authenticated, false otherwise
 */
export function isAuthenticated() {
  const hasToken = hasAuthToken()
  const tokenExpired = isTokenExpired()

  const authenticated = hasToken && !tokenExpired

  if (!authenticated) {
    console.log('User not authenticated:', {
      hasToken,
      tokenExpired,
      token: hasToken ? 'exists' : 'missing',
      expireTime: getTokenExpireTime(),
    })
  }

  return authenticated
}

/**
 * Clear all authentication data from localStorage
 * This function should be called on logout or when token is invalid
 */
export function clearAuthData() {
  const keysToRemove = ['token', 'auth_token', 'token_expire_time', 'user_info']

  keysToRemove.forEach((key) => {
    localStorage.removeItem(key)
  })

  console.log('Authentication data cleared from localStorage')
}

/**
 * Validate token format (basic validation)
 * @param {string} token - Token to validate
 * @returns {boolean} True if token format appears valid
 */
export function isValidTokenFormat(token) {
  if (!token || typeof token !== 'string') {
    return false
  }

  // Basic validation - token should be a non-empty string with reasonable length
  const trimmedToken = token.trim()
  return trimmedToken.length >= 10 && trimmedToken.length <= 500
}

/**
 * Get authentication headers for API requests
 * @returns {Object} Headers object with authentication token
 */
export function getAuthHeaders() {
  const token = getAuthToken()

  if (!token) {
    return {}
  }

  return {
    token: token,
  }
}

/**
 * Check if current route should be accessible without authentication
 * @param {string} path - Route path to check
 * @returns {boolean} True if route is public, false if requires authentication
 */
export function isPublicRoute(path) {
  const publicRoutes = [
    '/login',
    '/404',
    '/:pathMatch(.*)', // 404 catch-all route
  ]

  return publicRoutes.some((route) => {
    if (route === path) return true

    // Handle dynamic routes like /:pathMatch(.*)
    if (route.includes(':pathMatch')) {
      return (
        !path.startsWith('/login') &&
        path !== '/' &&
        !path.startsWith('/inductive') &&
        !path.startsWith('/chat') &&
        !path.startsWith('/user-center') &&
        !path.startsWith('/attribution-tasks') &&
        !path.startsWith('/result') &&
        !path.startsWith('/admin')
      )
    }

    return false
  })
}

/**
 * Get redirect path after successful login
 * @param {Object} route - Current route object
 * @returns {string} Path to redirect to after login
 */
export function getRedirectPath(route) {
  // If there's a redirect query parameter, use it
  if (route.query && route.query.redirect) {
    return route.query.redirect
  }

  // Default to home page
  return '/'
}
