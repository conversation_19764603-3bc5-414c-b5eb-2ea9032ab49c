/**
 * Axios interceptors for handling authentication and API errors globally
 */
import axios from 'axios'
import { clearAuthData, getAuthToken } from './auth'
import { useStates } from '../store/states'
import router from '../router'

/**
 * Setup axios interceptors for authentication and error handling
 */
export function setupAxiosInterceptors() {
  // Request interceptor to add authentication token
  axios.interceptors.request.use(
    (config) => {
      const token = getAuthToken()
      if (token) {
        config.headers.token = token
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    },
  )

  // Response interceptor to handle authentication errors
  axios.interceptors.response.use(
    (response) => {
      return response
    },
    (error) => {
      // Handle authentication errors
      if (error.response) {
        const { status, data } = error.response

        // Handle 401 Unauthorized
        if (status === 401) {
          console.log('401 Unauthorized - clearing auth data and redirecting to login')
          handleAuthError()
          return Promise.reject(error)
        }

        // Handle API error codes for authentication failures
        if (data && data.error_code === 301) {
          console.log('API error code 301 - authentication failed')
          handleAuthError()
          return Promise.reject(error)
        }

        // Handle token expiration (if API returns specific error code)
        if (data && (data.error_code === 302 || data.message?.includes('token'))) {
          console.log('Token expired or invalid - clearing auth data')
          handleAuthError()
          return Promise.reject(error)
        }
      }

      return Promise.reject(error)
    },
  )
}

/**
 * Handle authentication errors by clearing auth data and redirecting to login
 */
function handleAuthError() {
  try {
    // Clear authentication data
    clearAuthData()

    // Clear store data
    const statesStore = useStates()
    statesStore.clearAllAuthData()

    // Redirect to login page with current path as redirect parameter
    const currentPath = router.currentRoute.value.fullPath
    if (currentPath !== '/login') {
      router.push({
        path: '/login',
        query: { redirect: currentPath },
      })
    }
  } catch (error) {
    console.error('Error handling auth error:', error)
    // Fallback: just redirect to login
    router.push('/login')
  }
}

/**
 * Remove axios interceptors (useful for testing or cleanup)
 */
export function removeAxiosInterceptors() {
  axios.interceptors.request.clear()
  axios.interceptors.response.clear()
}
