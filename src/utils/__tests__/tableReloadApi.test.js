import { transformReloadedData } from '../tableReloadApi'

// Test data with enhanced table_info structure
const mockEnhancedApiResponse = {
  table_data: {
    name: ['a', 'b', 'c'],
    price: [100, 200, 50],
    score: [null, 2.0, 3.0]
  },
  table_id: '2_1_d1decaeb-43e2-4e63-b071-35c54d82ded9',
  table_info: {
    output_df_dict: {
      name: {
        column_index: 0,
        dirty_count: 0,
        dirty_rate: 0.0,
        dtype: 'string',
        missing_count: 0,
        missing_rate: 0.0
      },
      price: {
        column_index: 1,
        dirty_count: 0,
        dirty_rate: 0.0,
        dtype: 'int',
        missing_count: 0,
        missing_rate: 0.0
      },
      score: {
        column_index: 2,
        dirty_count: 1,
        dirty_rate: 0.3333333333333333,
        dtype: 'int',
        missing_count: 1,
        missing_rate: 0.3333333333333333
      }
    },
    output_info_dict: {
      duplicate_rows: 0,
      overall_dirty_rate: 0.1111111111111111,
      overall_missing_rate: 0.1111111111111111,
      quality_score: 88,
      total_columns: 3,
      total_rows: 3
    },
    output_type_dict: {
      float_count: 0,
      int_count: 2,
      string_count: 1
    }
  }
}

// Test data with legacy table_info structure
const mockLegacyApiResponse = {
  table_data: {
    name: ['a', 'b', 'c'],
    price: [100, 200, 50]
  },
  table_id: 'legacy_table_123',
  table_info: {
    name: {
      dtype: 'string',
      missing_count: 0,
      outlier_count: 0
    },
    price: {
      dtype: 'int',
      missing_count: 0,
      outlier_count: 0
    }
  }
}

describe('transformReloadedData', () => {
  test('should handle enhanced table_info structure correctly', () => {
    const result = transformReloadedData(mockEnhancedApiResponse, 'test_table_id')
    
    // Check basic structure
    expect(result).toHaveProperty('columns')
    expect(result).toHaveProperty('rows')
    expect(result).toHaveProperty('table_id')
    expect(result).toHaveProperty('table_info')
    expect(result).toHaveProperty('table_data')
    
    // Check table_id
    expect(result.table_id).toBe('2_1_d1decaeb-43e2-4e63-b071-35c54d82ded9')
    
    // Check columns structure with enhanced metadata
    expect(result.columns).toHaveLength(3)
    expect(result.columns[0]).toMatchObject({
      field: 'name',
      title: 'name',
      dtype: 'string',
      missing_count: 0,
      missing_rate: 0.0,
      dirty_count: 0,
      dirty_rate: 0.0,
      column_index: 0
    })
    
    expect(result.columns[2]).toMatchObject({
      field: 'score',
      title: 'score',
      dtype: 'int',
      missing_count: 1,
      missing_rate: 0.3333333333333333,
      dirty_count: 1,
      dirty_rate: 0.3333333333333333,
      column_index: 2
    })
    
    // Check rows transformation
    expect(result.rows).toHaveLength(3)
    expect(result.rows[0]).toEqual({
      name: 'a',
      price: 100,
      score: null
    })
    
    // Check that complete table_info is preserved
    expect(result.table_info).toEqual(mockEnhancedApiResponse.table_info)
  })
  
  test('should handle legacy table_info structure correctly', () => {
    const result = transformReloadedData(mockLegacyApiResponse, 'test_table_id')
    
    // Check basic structure
    expect(result).toHaveProperty('columns')
    expect(result).toHaveProperty('rows')
    expect(result).toHaveProperty('table_id')
    
    // Check columns structure with legacy metadata
    expect(result.columns).toHaveLength(2)
    expect(result.columns[0]).toMatchObject({
      field: 'name',
      title: 'name',
      dtype: 'string',
      missing_count: 0,
      outlier_count: 0
    })
    
    // Check that legacy table_info is preserved
    expect(result.table_info).toEqual(mockLegacyApiResponse.table_info)
  })
  
  test('should handle missing table_info gracefully', () => {
    const responseWithoutTableInfo = {
      table_data: {
        name: ['a', 'b'],
        value: [1, 2]
      },
      table_id: 'no_info_table'
    }
    
    const result = transformReloadedData(responseWithoutTableInfo, 'test_table_id')
    
    // Should still work with fallback column inference
    expect(result.columns).toHaveLength(2)
    expect(result.columns[0]).toMatchObject({
      field: 'name',
      title: 'name',
      dtype: 'unknown',
      ftype: 'unknown',
      missing_count: 0,
      outlier_count: 0
    })
  })
  
  test('should throw error for invalid table_data', () => {
    const invalidResponse = {
      table_id: 'test_id'
      // missing table_data
    }
    
    expect(() => {
      transformReloadedData(invalidResponse, 'test_table_id')
    }).toThrow('Invalid table data in API response')
  })
})

// Manual testing function for development
export const testEnhancedTableInfo = () => {
  console.log('Testing enhanced table_info handling...')
  
  try {
    const result = transformReloadedData(mockEnhancedApiResponse, 'test_table_id')
    console.log('✅ Enhanced table_info test passed')
    console.log('Columns:', result.columns)
    console.log('Table info:', result.table_info)
    
    const legacyResult = transformReloadedData(mockLegacyApiResponse, 'test_table_id')
    console.log('✅ Legacy table_info test passed')
    console.log('Legacy columns:', legacyResult.columns)
    
    return true
  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}
