import { createRouter, createWebHistory } from 'vue-router'
import { isAuthenticated, clearAuthData } from '../utils/auth'
import { useStates } from '../store/states'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: () => import('../views/HomeView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/inductive',
      component: () => import('../views/InductiveView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/chat',
      component: () => import('../views/ChatView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/chat-isd',
      component: () => import('../views/ChatISDView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/user-center',
      component: () => import('../views/UserCenter.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresAuth: false },
    },
    {
      path: '/attribution-tasks',
      name: 'TaskView',
      component: () => import('../views/TaskView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/result/:taskId/:instanceId',
      // path: '/result/1/1-1',
      name: 'ResultView',
      component: () => import('../views/ResultView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin',
      name: 'AdminConsole',
      component: () => import('../views/AdminConsole.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/:pathMatch(.*)',
      component: () => import('../views/NotFoundView.vue'),
      meta: { requiresAuth: false },
    },
  ],
})

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  console.log('Navigation guard triggered:', {
    to: to.path,
    from: from.path,
    requiresAuth: to.meta.requiresAuth,
  })

  // Check if route requires authentication
  if (to.meta.requiresAuth !== false) {
    // Route requires authentication (default behavior)
    if (!isAuthenticated()) {
      console.log('User not authenticated, redirecting to login')

      // Clear any invalid auth data
      clearAuthData()

      // Clear auth data from store as well
      try {
        const statesStore = useStates()
        statesStore.clearAllAuthData()
      } catch (error) {
        console.warn('Could not clear store auth data:', error)
      }

      // Redirect to login with the intended destination
      const redirectPath = to.path !== '/login' ? to.fullPath : '/'
      next({
        path: '/login',
        query: { redirect: redirectPath },
      })
      return
    }

    // Check admin routes
    if (to.meta.requiresAdmin) {
      try {
        const statesStore = useStates()
        const userRole = statesStore.userRole

        if (userRole !== 'admin') {
          console.log('User does not have admin privileges, redirecting to home')
          next('/')
          return
        }
      } catch (error) {
        console.warn('Could not check admin privileges:', error)
        // Allow navigation if we can't check (fail open for admin routes)
      }
    }
  } else {
    // Route does not require authentication
    if (to.path === '/login' && isAuthenticated()) {
      // User is already authenticated and trying to access login page
      // Redirect to home or intended destination
      const redirectPath = to.query.redirect || '/'
      console.log('User already authenticated, redirecting to:', redirectPath)
      next(redirectPath)
      return
    }
  }

  // Allow navigation
  next()
})

export default router
