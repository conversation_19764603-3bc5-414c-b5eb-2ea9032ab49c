server {
    listen 8888;
    server_name localhost;
    location / {
        root $ROOT_DIR;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    location ~* /api {
        proxy_pass $BACKEND_URL;

        # Completely disable proxy buffering
        # Disable the following when making docker images.
        proxy_buffering off;
        proxy_request_buffering off;
        
        proxy_read_timeout 300s;
        proxy_connect_timeout 30s;
    }

    # You can enable the following when making docker images.
    # error_page 500 502 503 504 /50x.html
    # location = /50x.html {
    #     root /usr/share/nginx/html;
    # }
}
